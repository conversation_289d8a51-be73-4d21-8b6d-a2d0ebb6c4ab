import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import type {} from '@redux-devtools/extension'; // devtools 类型支持
import type { RegisterFormData } from '@/types';

interface RegisterState {
  formData: RegisterFormData;
  currentStep: number;
  updateFormData: (data: Partial<RegisterFormData>) => void;
  setCurrentStep: (step: number) => void;
  clearFormData: () => void;
}

export const useRegisterStore = create<RegisterState>()(
  devtools(
    persist(
      (set, get) => ({
        formData: {},
        currentStep: 1,
        updateFormData: data => {
          set({
            formData: { ...get().formData, ...data },
          });
        },
        setCurrentStep: step => {
          set({ currentStep: step });
        },
        clearFormData: () => {
          set({ currentStep: 1, formData: {} });
        },
      }),
      {
        name: 'register-store', // localStorage持久化的名称
      },
    ),
    {
      name: 'RegisterStore', // DevTools 中显示的名称
    },
  ),
);
